'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuthStore } from '@/lib/stores/auth-store';
import { 
  projectsAPI, 
  clientsAPI, 
  tasksAPI, 
  teamAPI, 
  financeAPI,
  commissionsAPI 
} from '@/lib/api';
import { showToast } from '@/lib/toast';

export interface DashboardMetrics {
  revenue: {
    current_month: number;
    last_month: number;
    growth: number;
    target: number;
    yearly_total: number;
  };
  projects: {
    active: number;
    completed_this_month: number;
    overdue: number;
    total: number;
    total_value: number;
    average_value: number;
  };
  clients: {
    total: number;
    new_this_month: number;
    happy: number;
    concerned: number;
    angry: number;
  };
  team: {
    total_members: number;
    active_tasks: number;
    completed_tasks_this_week: number;
    average_productivity: number;
  };
  commissions: {
    total_pending: number;
    total_paid: number;
    pending_amount: number;
    paid_amount: number;
  };
}

export interface DashboardData {
  metrics: DashboardMetrics;
  recentProjects: any[];
  urgentTasks: any[];
  teamPerformance: any[];
  financialOverview: any;
  recentActivities: any[];
  systemHealth: {
    status: 'healthy' | 'warning' | 'error';
    uptime: string;
    lastUpdate: string;
  };
}

export function useDashboardData() {
  const { isAuthenticated } = useAuthStore();
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const fetchDashboardData = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch all dashboard data in parallel
      const [
        financialOverview,
        projectsData,
        clientsData,
        tasksData,
        teamData,
        teamStats,
        commissionsData
      ] = await Promise.all([
        financeAPI.getFinancialOverview().catch(() => null),
        projectsAPI.getProjects({ page_size: 10, ordering: '-created_at' }).catch(() => ({ results: [] })),
        clientsAPI.getClients({ page_size: 100 }).catch(() => ({ results: [] })),
        tasksAPI.getTasks({ priority: 'high,urgent', page_size: 10 }).catch(() => ({ results: [] })),
        teamAPI.getTeamMembers({ page_size: 10 }).catch(() => ({ results: [] })),
        teamAPI.getTeamStats().catch(() => null),
        commissionsAPI.getCommissionStats().catch(() => null)
      ]);

      // Process and aggregate the data
      const processedData = await processDashboardData({
        financialOverview,
        projectsData,
        clientsData,
        tasksData,
        teamData,
        teamStats,
        commissionsData
      });

      setData(processedData);
      setLastRefresh(new Date());
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('فشل في تحميل بيانات لوحة التحكم');
      showToast.error('فشل في تحميل بيانات لوحة التحكم');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // Auto-refresh every 5 minutes
  useEffect(() => {
    if (!isAuthenticated) return;

    fetchDashboardData();
    
    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000); // 5 minutes
    
    return () => clearInterval(interval);
  }, [isAuthenticated, fetchDashboardData]);

  const refreshData = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    data,
    loading,
    error,
    lastRefresh,
    refreshData,
    isStale: Date.now() - lastRefresh.getTime() > 10 * 60 * 1000 // 10 minutes
  };
}

async function processDashboardData(rawData: any): Promise<DashboardData> {
  const {
    financialOverview,
    projectsData,
    clientsData,
    tasksData,
    teamData,
    teamStats,
    commissionsData
  } = rawData;

  // Calculate metrics from real data
  const projects = projectsData.results || [];
  const clients = clientsData.results || [];
  const tasks = tasksData.results || [];
  const team = teamData.results || [];

  // Revenue metrics
  const revenue = {
    current_month: financialOverview?.monthly_revenue || 0,
    last_month: financialOverview?.previous_month_revenue || 0,
    growth: financialOverview?.revenue_growth || 0,
    target: financialOverview?.monthly_target || 100000,
    yearly_total: financialOverview?.yearly_revenue || 0
  };

  // Project metrics
  const activeProjects = projects.filter((p: any) => 
    ['planning', 'development', 'testing', 'review'].includes(p.status)
  );
  const completedThisMonth = projects.filter((p: any) => {
    const completedDate = new Date(p.completed_at || p.updated_at);
    const thisMonth = new Date();
    return p.status === 'completed' && 
           completedDate.getMonth() === thisMonth.getMonth() &&
           completedDate.getFullYear() === thisMonth.getFullYear();
  });
  const overdueProjects = projects.filter((p: any) => {
    const deadline = new Date(p.deadline);
    return p.status !== 'completed' && deadline < new Date();
  });

  const projectMetrics = {
    active: activeProjects.length,
    completed_this_month: completedThisMonth.length,
    overdue: overdueProjects.length,
    total: projects.length,
    total_value: projects.reduce((sum: number, p: any) => sum + (p.budget || 0), 0),
    average_value: projects.length > 0 ? 
      projects.reduce((sum: number, p: any) => sum + (p.budget || 0), 0) / projects.length : 0
  };

  // Client metrics
  const newClientsThisMonth = clients.filter((c: any) => {
    const createdDate = new Date(c.created_at);
    const thisMonth = new Date();
    return createdDate.getMonth() === thisMonth.getMonth() &&
           createdDate.getFullYear() === thisMonth.getFullYear();
  });

  const clientMetrics = {
    total: clients.length,
    new_this_month: newClientsThisMonth.length,
    happy: clients.filter((c: any) => c.satisfaction_level === 'satisfied').length,
    concerned: clients.filter((c: any) => c.satisfaction_level === 'neutral').length,
    angry: clients.filter((c: any) => c.satisfaction_level === 'unsatisfied').length
  };

  // Team metrics
  const teamMetrics = {
    total_members: team.length,
    active_tasks: teamStats?.active_tasks || 0,
    completed_tasks_this_week: teamStats?.completed_tasks_this_week || 0,
    average_productivity: teamStats?.average_productivity || 0
  };

  // Commission metrics
  const commissionMetrics = {
    total_pending: commissionsData?.pending_count || 0,
    total_paid: commissionsData?.paid_count || 0,
    pending_amount: commissionsData?.pending_amount || 0,
    paid_amount: commissionsData?.paid_amount || 0
  };

  return {
    metrics: {
      revenue,
      projects: projectMetrics,
      clients: clientMetrics,
      team: teamMetrics,
      commissions: commissionMetrics
    },
    recentProjects: projects.slice(0, 3),
    urgentTasks: tasks.filter((t: any) => ['urgent', 'high'].includes(t.priority)),
    teamPerformance: team.slice(0, 4),
    financialOverview: financialOverview || {},
    recentActivities: [], // Will be implemented with activity tracking
    systemHealth: {
      status: 'healthy',
      uptime: '99.9%',
      lastUpdate: new Date().toISOString()
    }
  };
}
