'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Users,
  FolderOpen,
  CheckSquare,
  TrendingUp,
  Calendar,
  AlertTriangle,
  Target,
  Activity,
  Clock,
  Award,
  BarChart3,
  Settings
} from 'lucide-react';
import { DEMO_ANALYTICS, DEMO_PROJECTS, DEMO_CLIENTS, DEMO_TASKS, DEMO_TEAM } from '@/lib/demo-data';
import { formatCurrency, formatRelativeTime, getStatusColor } from '@mtbrmg/shared';
import { UnifiedLayout } from '@/components/layout';
import { CurrencyIcon } from '@/components/ui/currency-icon';
import { ConvertedAmountDisplay } from '@/components/ui/currency-conversion-indicator';
import { useCurrency, useCurrencyStore, initializeCurrencyStore } from '@/lib/stores/currency-store';
import { useAuthStore } from '@/lib/stores/auth-store';
import { RoleGuard } from '@/components/auth/role-guard';
import { UserRole } from '@mtbrmg/shared';

export default function FounderDashboardPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const { format: formatCurrencyWithStore, refreshRates } = useCurrency();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Currency store now initializes immediately with manual/default rates - no background operations needed
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل لوحة تحكم المؤسس...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, show login prompt
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى لوحة تحكم المؤسس</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  const { revenue, projects, clients, team } = DEMO_ANALYTICS;

  // Debug logging
  console.log('FounderDashboard - mounted:', mounted);
  console.log('FounderDashboard - isAuthenticated:', isAuthenticated);
  console.log('FounderDashboard - user:', user);
  console.log('FounderDashboard - DEMO_ANALYTICS:', DEMO_ANALYTICS);

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">

        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              لوحة تحكم المؤسس - MTBRMG ERP
            </h1>
            <p className="text-gray-600 mt-1">
              نظرة شاملة على أداء الوكالة الرقمية
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="text-green-600 border-green-600">
              النظام يعمل بكفاءة
            </Badge>
            <Button variant="outline">
              تصدير التقرير
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الإيرادات الشهرية</CardTitle>
              <CurrencyIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <ConvertedAmountDisplay
                amount={revenue.current_month}
                className="text-2xl font-bold"
                showOriginal={true}
              />
              <div className="flex items-center text-xs text-green-600">
                <TrendingUp className="h-3 w-3 ml-1" />
                +{revenue.growth}% من الشهر الماضي
              </div>
              <Progress value={(revenue.current_month / revenue.target) * 100} className="mt-2" />
              <p className="text-xs text-muted-foreground mt-1">
                الهدف: {formatCurrency(revenue.target)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المشاريع النشطة</CardTitle>
              <FolderOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{projects.active}</div>
              <p className="text-xs text-muted-foreground">
                {projects.completed_this_month} مكتمل هذا الشهر
              </p>
              {projects.overdue > 0 && (
                <div className="flex items-center text-xs text-red-600 mt-1">
                  <AlertTriangle className="h-3 w-3 ml-1" />
                  {projects.overdue} متأخر عن الموعد
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">العملاء</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{clients.total}</div>
              <p className="text-xs text-muted-foreground">
                +{clients.new_this_month} عميل جديد هذا الشهر
              </p>
              <div className="flex gap-2 mt-2">
                <Badge variant="secondary" className="text-xs">
                  😊 {clients.happy}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  😟 {clients.concerned}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إنتاجية الفريق</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{team.average_productivity}%</div>
              <p className="text-xs text-muted-foreground">
                {team.completed_tasks_this_week} مهمة مكتملة هذا الأسبوع
              </p>
              <Progress value={team.average_productivity} className="mt-2" />
            </CardContent>
          </Card>
        </div>

        {/* Clients Section */}
        <div id="clients" className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                إدارة العملاء
              </CardTitle>
              <CardDescription>
                نظرة عامة على العملاء وحالتهم
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {DEMO_CLIENTS.slice(0, 6).map((client) => (
                  <div key={client.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{client.name}</h4>
                      <Badge variant="outline">
                        {client.status === 'active' && 'نشط'}
                        {client.status === 'inactive' && 'غير نشط'}
                        {client.status === 'pending' && 'معلق'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{client.email}</p>
                    <p className="text-sm text-gray-600">{client.phone}</p>
                    <div className="mt-2 text-xs text-gray-500">
                      {client.projects_count} مشروع
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts and Detailed Views */}
        <div id="projects" className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Recent Projects */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5" />
                المشاريع الحديثة
              </CardTitle>
              <CardDescription>
                آخر المشاريع وحالة التقدم
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {DEMO_PROJECTS.slice(0, 3).map((project) => (
                  <div key={project.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{project.name}</h4>
                      <p className="text-sm text-gray-600">{project.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge className={getStatusColor(project.status)}>
                          {project.status === 'development' && 'قيد التطوير'}
                          {project.status === 'testing' && 'الاختبار'}
                          {project.status === 'planning' && 'التخطيط'}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {formatCurrency(project.budget || 0)}
                        </span>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className="text-2xl font-bold text-purple-600">
                        {project.progress}%
                      </div>
                      <Progress value={project.progress} className="w-16 mt-1" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Team Performance */}
          <Card id="team">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                أداء الفريق
              </CardTitle>
              <CardDescription>
                إحصائيات أعضاء الفريق
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {DEMO_TEAM.slice(0, 4).map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                        {member.name.charAt(0)}
                      </div>
                      <div>
                        <h4 className="font-medium">{member.name}</h4>
                        <p className="text-sm text-gray-600">
                          {member.role === 'sales_manager' && 'مدير المبيعات'}
                          {member.role === 'developer' && 'مطور'}
                          {member.role === 'designer' && 'مصمم'}
                          {member.role === 'media_buyer' && 'مشتري إعلانات'}
                          {member.role === 'wordpress_developer' && 'مطور ووردبريس'}
                        </p>
                      </div>
                    </div>
                    <div className="text-left">
                      <Badge variant="outline">
                        {member.role === 'sales_manager' && `${member.clients_count} عميل`}
                        {member.role === 'developer' && `${member.projects_completed} مشروع`}
                        {member.role === 'designer' && `${member.designs_completed} تصميم`}
                        {member.role === 'media_buyer' && `${member.campaigns_managed} حملة`}
                        {member.role === 'wordpress_developer' && `${member.sites_built} موقع`}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity & Urgent Tasks */}
        <div id="tasks" className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Urgent Tasks */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                المهام العاجلة
              </CardTitle>
              <CardDescription>
                المهام التي تحتاج إلى انتباه فوري
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {DEMO_TASKS.filter(task => task.priority === 'urgent' || task.priority === 'high').map((task) => (
                  <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{task.title}</h4>
                      <p className="text-sm text-gray-600">{task.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge className={getStatusColor(task.priority)}>
                          {task.priority === 'urgent' && 'عاجل'}
                          {task.priority === 'high' && 'عالي'}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {task.estimated_hours} ساعة
                        </span>
                      </div>
                    </div>
                    <div className="text-left">
                      <Badge variant="outline" className={getStatusColor(task.status)}>
                        {task.status === 'in_progress' && 'قيد التنفيذ'}
                        {task.status === 'review' && 'مراجعة'}
                        {task.status === 'todo' && 'قائمة المهام'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Financial Overview */}
          <Card id="analytics">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                النظرة المالية
              </CardTitle>
              <CardDescription>
                ملخص الأداء المالي
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-green-800">إجمالي الإيرادات</h4>
                    <p className="text-sm text-green-600">هذا الشهر</p>
                  </div>
                  <div className="text-2xl font-bold text-green-800">
                    {formatCurrency(revenue.current_month)}
                  </div>
                </div>
                
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-blue-800">المشاريع الجارية</h4>
                    <p className="text-sm text-blue-600">القيمة الإجمالية</p>
                  </div>
                  <div className="text-2xl font-bold text-blue-800">
                    {formatCurrency(DEMO_PROJECTS.reduce((sum, p) => sum + (p.budget || 0), 0))}
                  </div>
                </div>

                <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-purple-800">متوسط قيمة المشروع</h4>
                    <p className="text-sm text-purple-600">لهذا العام</p>
                  </div>
                  <div className="text-2xl font-bold text-purple-800">
                    {formatCurrency(60000)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Settings Section */}
        <div id="settings" className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                إعدادات النظام
              </CardTitle>
              <CardDescription>
                إعدادات عامة ومعلومات النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">معلومات المؤسس</h4>
                  <div className="space-y-2">
                    <p><strong>الاسم:</strong> {user?.first_name} {user?.last_name}</p>
                    <p><strong>البريد الإلكتروني:</strong> {user?.email}</p>
                    <p><strong>الهاتف:</strong> {user?.phone}</p>
                    <p><strong>الدور:</strong> مؤسس النظام</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="font-medium">إحصائيات النظام</h4>
                  <div className="space-y-2">
                    <p><strong>إجمالي العملاء:</strong> {clients.total}</p>
                    <p><strong>المشاريع النشطة:</strong> {projects.active}</p>
                    <p><strong>أعضاء الفريق:</strong> {DEMO_TEAM.length}</p>
                    <p><strong>المهام المكتملة:</strong> {team.completed_tasks_this_week}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedLayout>
  );
}
