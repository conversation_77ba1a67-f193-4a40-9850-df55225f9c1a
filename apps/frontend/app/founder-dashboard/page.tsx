'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Users,
  FolderOpen,
  CheckSquare,
  TrendingUp,
  Calendar,
  AlertTriangle,
  Target,
  Activity,
  Clock,
  Award,
  BarChart3,
  Settings,
  RefreshCw,
  Download,
  Zap
} from 'lucide-react';
import { DEMO_ANALYTICS, DEMO_PROJECTS, DEMO_CLIENTS, DEMO_TASKS, DEMO_TEAM } from '@/lib/demo-data';
import { formatCurrency, formatRelativeTime, getStatusColor } from '@mtbrmg/shared';
import { UnifiedLayout } from '@/components/layout';
import { CurrencyIcon } from '@/components/ui/currency-icon';
import { ConvertedAmountDisplay } from '@/components/ui/currency-conversion-indicator';
import { useCurrency, useCurrencyStore, initializeCurrencyStore } from '@/lib/stores/currency-store';
import { useDashboardData } from '@/hooks/use-dashboard-data';
import { QuickActions, QuickStats, RecentActivities } from '@/components/dashboard/quick-actions';
import {
  ClickableCard,
  MetricCard,
  ProjectCard,
  TaskCard,
  TeamMemberCard
} from '@/components/dashboard/clickable-card';
import { showToast } from '@/lib/toast';
import { useAuthStore } from '@/lib/stores/auth-store';
import { useCurrency } from '@/lib/stores/currency-store';
import { RoleGuard } from '@/components/auth/role-guard';
import { UserRole } from '@mtbrmg/shared';
import { exportDashboard } from '@/lib/dashboard-export';

export default function FounderDashboardPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const { format: formatCurrencyWithStore, refreshRates } = useCurrency();
  const { data, loading, error, lastRefresh, refreshData, isStale } = useDashboardData();
  const [mounted, setMounted] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Currency store now initializes immediately with manual/default rates - no background operations needed
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  // Handle manual refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
      showToast.success('تم تحديث البيانات بنجاح');
    } catch (error) {
      showToast.error('فشل في تحديث البيانات');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle export functionality
  const handleExport = async () => {
    setIsExporting(true);
    try {
      if (!dashboardData) {
        throw new Error('لا توجد بيانات للتصدير');
      }

      await exportDashboard(dashboardData, 'pdf', {
        sections: ['summary', 'financial', 'projects', 'clients', 'team'],
        includeCharts: true,
        language: 'ar'
      });

      showToast.success('تم تصدير التقرير بنجاح');
    } catch (error) {
      console.error('Export error:', error);
      showToast.error('فشل في تصدير التقرير');
    } finally {
      setIsExporting(false);
    }
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل لوحة تحكم المؤسس...</p>
        </div>
      </div>
    );
  }

  // Show loading state for initial data fetch
  if (loading && !data) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-600" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">جاري تحميل البيانات</h2>
              <p className="text-gray-600">يرجى الانتظار بينما نقوم بتحميل بيانات لوحة التحكم...</p>
            </div>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  // If not authenticated, show login prompt
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى لوحة تحكم المؤسس</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Fallback to demo data if real data is not available
  const dashboardData = data || {
    metrics: DEMO_ANALYTICS,
    recentProjects: DEMO_PROJECTS.slice(0, 3),
    urgentTasks: DEMO_TASKS.filter(task => task.priority === 'urgent' || task.priority === 'high'),
    teamPerformance: DEMO_TEAM.slice(0, 4),
    financialOverview: {},
    recentActivities: [],
    systemHealth: { status: 'healthy' as const, uptime: '99.9%', lastUpdate: new Date().toISOString() }
  };

  const { revenue, projects, clients, team } = dashboardData.metrics;

  // Debug logging
  console.log('FounderDashboard - mounted:', mounted);
  console.log('FounderDashboard - isAuthenticated:', isAuthenticated);
  console.log('FounderDashboard - user:', user);
  console.log('FounderDashboard - dashboardData:', dashboardData);
  console.log('FounderDashboard - loading:', loading);
  console.log('FounderDashboard - error:', error);

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">

        {/* Error Banner */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <h3 className="font-medium text-red-800">خطأ في تحميل البيانات</h3>
                <p className="text-sm text-red-600">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  className="mt-2"
                >
                  إعادة المحاولة
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              لوحة تحكم المؤسس - MTBRMG ERP
            </h1>
            <p className="text-gray-600 mt-1">
              نظرة شاملة على أداء الوكالة الرقمية
            </p>
            {lastRefresh && (
              <p className="text-xs text-gray-500 mt-1">
                آخر تحديث: {lastRefresh.toLocaleTimeString('ar-EG')}
                {isStale && <span className="text-orange-500 mr-2">• البيانات قديمة</span>}
              </p>
            )}
          </div>
          <div className="flex items-center gap-4">
            <Badge
              variant="outline"
              className={`${dashboardData.systemHealth.status === 'healthy' ? 'text-green-600 border-green-600' : 'text-orange-600 border-orange-600'}`}
            >
              {dashboardData.systemHealth.status === 'healthy' ? 'النظام يعمل بكفاءة' : 'تحذير النظام'}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 ml-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'جاري التحديث...' : 'تحديث البيانات'}
            </Button>
            <Button
              variant="outline"
              onClick={handleExport}
              disabled={isExporting}
            >
              <Download className="h-4 w-4 ml-2" />
              {isExporting ? 'جاري التصدير...' : 'تصدير التقرير'}
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <MetricCard
            title="الإيرادات الشهرية"
            value={revenue.current_month}
            change={revenue.growth}
            changeLabel="من الشهر الماضي"
            icon={CurrencyIcon}
            href="/founder-dashboard/finance/revenue"
            progress={(revenue.current_month / revenue.target) * 100}
            target={revenue.target}
            valuePrefix=""
            valueSuffix=""
            trend={revenue.growth > 0 ? 'up' : revenue.growth < 0 ? 'down' : 'neutral'}
          />

          <MetricCard
            title="المشاريع النشطة"
            value={projects.active}
            changeLabel={`${projects.completed_this_month} مكتمل هذا الشهر`}
            icon={FolderOpen}
            href="/founder-dashboard/projects"
          />

          <MetricCard
            title="العملاء"
            value={clients.total}
            changeLabel={`+${clients.new_this_month} عميل جديد هذا الشهر`}
            icon={Users}
            href="/founder-dashboard/clients"
          />

          <MetricCard
            title="إنتاجية الفريق"
            value={`${team.average_productivity}%`}
            changeLabel={`${team.completed_tasks_this_week} مهمة مكتملة هذا الأسبوع`}
            icon={Activity}
            href="/founder-dashboard/team"
            progress={team.average_productivity}
          />
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <QuickActions />
        </div>

        {/* Clients Section */}
        <div id="clients" className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                إدارة العملاء
              </CardTitle>
              <CardDescription>
                نظرة عامة على العملاء وحالتهم
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {DEMO_CLIENTS.slice(0, 6).map((client) => (
                  <div key={client.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{client.name}</h4>
                      <Badge variant="outline">
                        {client.status === 'active' && 'نشط'}
                        {client.status === 'inactive' && 'غير نشط'}
                        {client.status === 'pending' && 'معلق'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{client.email}</p>
                    <p className="text-sm text-gray-600">{client.phone}</p>
                    <div className="mt-2 text-xs text-gray-500">
                      {client.projects_count} مشروع
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts and Detailed Views */}
        <div id="projects" className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Recent Projects */}
          <ClickableCard
            title="المشاريع الحديثة"
            description="آخر المشاريع وحالة التقدم"
            icon={FolderOpen}
            href="/founder-dashboard/projects"
            hoverable={false}
          >
            <div className="space-y-4">
              {dashboardData.recentProjects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  onClick={(proj) => router.push(`/founder-dashboard/projects/${proj.id}`)}
                />
              ))}
              {dashboardData.recentProjects.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <FolderOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>لا توجد مشاريع حديثة</p>
                </div>
              )}
            </div>
          </ClickableCard>

          {/* Team Performance */}
          <ClickableCard
            title="أداء الفريق"
            description="إحصائيات أعضاء الفريق"
            icon={Users}
            href="/founder-dashboard/team"
            hoverable={false}
          >
            <div className="space-y-4">
              {dashboardData.teamPerformance.map((member) => (
                <TeamMemberCard
                  key={member.id}
                  member={{
                    id: member.id,
                    name: member.name,
                    role: member.role,
                    department: member.role === 'sales_manager' ? 'sales' :
                               member.role === 'developer' ? 'development' :
                               member.role === 'designer' ? 'design' :
                               member.role === 'media_buyer' ? 'media_buying' : 'other',
                    avatar: member.avatar,
                    performance_score: 85, // Default performance score
                    active_tasks: member.role === 'developer' ? 5 : 3,
                    completed_projects: member.projects_completed || member.designs_completed || member.campaigns_managed || 0
                  }}
                  onClick={(mem) => router.push(`/founder-dashboard/team/${mem.department}/${mem.id}`)}
                />
              ))}
              {dashboardData.teamPerformance.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>لا توجد بيانات أعضاء الفريق</p>
                </div>
              )}
            </div>
          </ClickableCard>
        </div>

        {/* Recent Activity & Urgent Tasks */}
        <div id="tasks" className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Urgent Tasks */}
          <ClickableCard
            title="المهام العاجلة"
            description="المهام التي تحتاج إلى انتباه فوري"
            icon={AlertTriangle}
            href="/founder-dashboard/tasks"
            hoverable={false}
            badge={dashboardData.urgentTasks.length > 0 ? `${dashboardData.urgentTasks.length}` : undefined}
            badgeVariant="destructive"
          >
            <div className="space-y-4">
              {dashboardData.urgentTasks.slice(0, 3).map((task) => (
                <TaskCard
                  key={task.id}
                  task={task}
                  onClick={(t) => router.push(`/founder-dashboard/tasks/${t.id}`)}
                />
              ))}
              {dashboardData.urgentTasks.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <CheckSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>لا توجد مهام عاجلة</p>
                </div>
              )}
            </div>
          </ClickableCard>

          {/* Recent Activities */}
          <RecentActivities />
        </div>

        {/* Financial Overview */}
        <div className="mb-8">
          <ClickableCard
            title="النظرة المالية"
            description="ملخص الأداء المالي"
            icon={BarChart3}
            href="/founder-dashboard/finance"
            hoverable={false}
          >
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg cursor-pointer hover:bg-green-100 transition-colors"
                   onClick={() => router.push('/founder-dashboard/finance/revenue')}>
                <div>
                  <h4 className="font-medium text-green-800">إجمالي الإيرادات</h4>
                  <p className="text-sm text-green-600">هذا الشهر</p>
                </div>
                <div className="text-2xl font-bold text-green-800">
                  <ConvertedAmountDisplay
                    amount={revenue.current_month}
                    className="text-2xl font-bold text-green-800"
                  />
                </div>
              </div>

              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors"
                   onClick={() => router.push('/founder-dashboard/projects')}>
                <div>
                  <h4 className="font-medium text-blue-800">المشاريع الجارية</h4>
                  <p className="text-sm text-blue-600">القيمة الإجمالية</p>
                </div>
                <div className="text-2xl font-bold text-blue-800">
                  <ConvertedAmountDisplay
                    amount={projects.total_value || DEMO_PROJECTS.reduce((sum, p) => sum + (p.budget || 0), 0)}
                    className="text-2xl font-bold text-blue-800"
                  />
                </div>
              </div>

              <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg cursor-pointer hover:bg-purple-100 transition-colors"
                   onClick={() => router.push('/founder-dashboard/analytics')}>
                <div>
                  <h4 className="font-medium text-purple-800">متوسط قيمة المشروع</h4>
                  <p className="text-sm text-purple-600">لهذا العام</p>
                </div>
                <div className="text-2xl font-bold text-purple-800">
                  <ConvertedAmountDisplay
                    amount={projects.average_value || 60000}
                    className="text-2xl font-bold text-purple-800"
                  />
                </div>
              </div>
            </div>
          </ClickableCard>
        </div>

        {/* Settings Section */}
        <div id="settings" className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                إعدادات النظام
              </CardTitle>
              <CardDescription>
                إعدادات عامة ومعلومات النظام
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">معلومات المؤسس</h4>
                  <div className="space-y-2">
                    <p><strong>الاسم:</strong> {user?.first_name} {user?.last_name}</p>
                    <p><strong>البريد الإلكتروني:</strong> {user?.email}</p>
                    <p><strong>الهاتف:</strong> {user?.phone}</p>
                    <p><strong>الدور:</strong> مؤسس النظام</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="font-medium">إحصائيات النظام</h4>
                  <div className="space-y-2">
                    <p><strong>إجمالي العملاء:</strong> {clients.total}</p>
                    <p><strong>المشاريع النشطة:</strong> {projects.active}</p>
                    <p><strong>أعضاء الفريق:</strong> {DEMO_TEAM.length}</p>
                    <p><strong>المهام المكتملة:</strong> {team.completed_tasks_this_week}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedLayout>
  );
}
